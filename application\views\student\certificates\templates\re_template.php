<ul class="breadcrumb">
<li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('student/Certificates_controller/index') ?>">Student Certificate</a></li>
    <li><a href="<?php echo site_url('student/Certificates_controller/issue_certificate/'.$student_uid) ?>">Issue Certificate</a></li>
    <li><?= $issued_data->template_name ?></li>
</ul>


<div class="col-md-12">
	<div class="card cd_border">
		<div class="card-header panel_heading_new_style_staff_border">
			<div class="row" style="margin: 0px">
				<div class="col-md-10">
					<h3 class="card-title panel_title_new_style_staff">
						<a class="back_anchor" href="<?php  echo site_url('student/Certificates_controller/issue_certificate/' . $student_uid); ?>">
						<span class="fa fa-arrow-left"></span>
						</a> 
						<strong><?php echo $issued_data->template_name; ?></strong> of <?php echo '<strong>' . $stdData->stdName . '</strong> (Class: ' . $stdData->className . ' / Section: ' . $stdData->sectionName . ') (Admission No: ' . $stdData->admission_no . ')' ?>
					</h3>
				</div>
                <ul class="panel-controls" style="float:right">
                    <button class="btn btn-success generate_button" id="" onClick="generate_certificate(<?php echo $issued_data->receipt_number_id; ?>); this.disabled = true;"><span class="glyphicon glyphicon-list-alt" aria-hidden="true"></span>Generate Certificate</button>
                    <!-- <button class="btn btn-danger" id="printBtn" onClick="printProfile()"><span class="glyphicon glyphicon-print" aria-hidden="true"></span>Issue and Print</button> -->
                    <!-- <button class="btn btn-primary" onClick="editTemplate()">Edit</button> -->
                </ul>
				<!-- <div class="col-md-2">
					<a href="<?php // echo site_url('student/Certificates_controller/generate_ceritificates/' . $student_uid); ?>" class="btn btn-primary" style="float: right;"> Issue New Certificate</a>
				</div> -->
			</div>
		</div>
        <div class="col-12 text-center loading-icon" style="display: none; padding: 100px">
        
            <i class="fa fa-spinner fa-spin" style="font-size: 40px;"></i>
            <center>
            <br>
            <h4>This process takes less than a minute...</h4>
            <h4>Displaying the certificate in PDF format in few seconds...</h4>
            </center>
        </div>
        <div id='ten_seconds' style="display: none;">
        </div>
        <?php
        //echo "<pre>"; print_r($issued_data); die();
        $jsonString = $issued_data->edited_data;
        $dataArray = json_decode($jsonString, true);
        //echo "<pre>"; print_r($dataArray); die();
        ?>
        <!--     </div>
        
    <div class="panel panel-default panel1"> -->
        <?php 
        $logo = (isset($school_logo) && !empty($school_logo)) ? site_url($school_logo) : '';
        $seal = (isset($school_seal) && !empty($school_seal)) ? site_url($school_seal) : '';
        $signature = (isset($signature) && !empty($signature)) ? site_url($signature) : '';
        // echo "<pre>"; print_r($seal); die();
        $logo_link = "<img width='100' height='100' src='".$logo."'>";
        $seal_link = "<img width='100' height='70' src='".$seal."'>";
        $signature_link = "<img width='100' height='40' src='".$signature."'>";
        // $logo_link = "<img width='100' height='100' src='".$this->filemanager->getFilePath($school_logo)."'>"; ?>
        <div class="panel-body" id="exporter" >
            <div class="row">
                <div class="form-group col-md-3">
                <select class="form-control" name="manual_fields" id="manual_fields">
                        <option value="">Manual Field</option>
                        <?php foreach ($manualfields as  $field) { 
                            $value = $field["value"];
                            $name = $field["name"];
                            echo '<option value="' . $value . '">' . $name . '</option>';
                        } ?>
                    </select>
                    <span class="help-block">Use these fields to edit data manually.</span>
                </div>
                <div class="form-group col-md-3">
                    <input name="manual_value" class="form-control input-md" id="manual_value" placeholder="Manual field value" value="">
                </div>
                <div class="col-md-2"><button class="btn btn-primary" onclick="addManualValue()">Add</button></div>
            </div>

            <hr>
           
            <div class="col-md-3"></div>
            <div id="aadhar" class="col-md-6">
                <?php
                $template_edit = $issued_data->certificate_data;
                $line1 = '';
                $line2 = '';
                $area = '';
                $district = '';
                $state = '';
                $country = '';
                $pincode = '';
                $htmlTemplate = '';
                $student_address = '';
                $stu_address = '';
                $birth_taluk = '';
                $birth_district = '' ;
                if (!empty($stdData->address)) {
                    $line1 = $stdData->address[0]->Address_line1;
                    $line2 = $stdData->address[0]->Address_line2;
                    $area = $stdData->address[0]->area;
                    $district = $stdData->address[0]->district;
                    $state = $stdData->address[0]->state;
                    $country = $stdData->address[0]->country;
                    $pincode = $stdData->address[0]->pin_code;
                    // $student_address = $stdData->address[0]->Addre
                } else if (!empty($stdData)) {
                    $birth_taluk = $stdData->birth_taluk;
                    $birth_district = $stdData->birth_district;
                }else if (!empty($stdFatherData->address)) {
                    $line1 = $stdFatherData->address[0]->Address_line1;
                    $line2 = $stdFatherData->address[0]->Address_line2;
                    $area = $stdFatherData->address[0]->area;
                    $district = $stdFatherData->address[0]->district;
                    $state = $stdFatherData->address[0]->state;
                    $country = $stdFatherData->address[0]->country;
                    $pincode = $stdFatherData->address[0]->pin_code;
                } else if (!empty($stdMotherData->address)) {
                    $line1 = $stdMotherData->address[0]->Address_line1;
                    $line2 = $stdMotherData->address[0]->Address_line2;
                    $area = $stdMotherData->address[0]->area;
                    $district = $stdMotherData->address[0]->district;
                    $state = $stdMotherData->address[0]->state;
                    $country = $stdMotherData->address[0]->country;
                    $pincode = $stdMotherData->address[0]->pin_code;
                }

                if ($line1 != '') {
                    $student_address .= $line1 . ', ';
                }
                if ($line2 != '') {
                    $student_address .= $line2 . ', ';
                }
                if ($area != '') {
                    $student_address .= $area . ', ';
                }
                if ($district != '') {
                    $student_address .= $district . ', ';
                }
                if ($state != '') {
                    $student_address .= $state . ', ';
                }
                if ($country != '') {
                    $student_address .= $country;
                }
                if ($pincode != '') {
                    $student_address .= ' - ' . $pincode;
                }

                if ($birth_taluk != '') {
                    $stu_address .= $birth_taluk . ', ';
                }
                if ($birth_district != '') {
                    $stu_address .= $birth_district ;
                }

                if ($stdData->gender == "M") {
                    $g = "MALE";
                    $a = "his";
                    $b = "S/o";
                    $c = 'Mr';
                } else {
                    $g = "FEMALE";
                    $a = "her";
                    $b = "D/o";
                    $c = 'Ms';
                }
                $physical_disability ='';
                $learning_disability ='';
                $sibilingStudying ='';
                $sibling_student_name ='';
                $sibling_school_name ='';
                $sibling_student_class ='';
                if(!empty($admission_from_data)){
                    $physical_disability = 'No';
                    if ($admission_from_data->physical_disability == 'Y') {
                        $physical_disability = 'Yes';
                    }
                    $learning_disability = 'No';
                    if ($admission_from_data->learning_disability == 'Y') {
                        $learning_disability = 'Yes';
                    }

                    $sibilingStudying = 'No';
                    if ($admission_from_data->sibling_student_class != '') {
                        $sibilingStudying = 'Yes';
                    }
                    
                    $sibling_student_name = '';
                    if ($admission_from_data->sibling_student_name != '') {
                        $sibling_student_name = $admission_from_data->sibling_student_name;
                    }
                    $sibling_school_name = '';
                    if ($admission_from_data->sibling_school_name != '') {
                        $sibling_school_name = $admission_from_data->sibling_school_name;
                    }
                    $sibling_student_class = '';
                    if ($admission_from_data->sibling_student_class != '') {
                        $sibling_student_class = $admission_from_data->sibling_student_class;
                    }
                } 
                

                $guardianName = '';
                $guardianEmail = '';
                $guardianMobile = '';
                $guardianDesignation = '';
                if (!empty($stdGuardianData)) {
                    $guardianName = $stdGuardianData->pName;
                    $guardianEmail = $stdGuardianData->email;
                    $guardianMobile = $stdGuardianData->mobile_no;
                    $guardianDesignation = $stdGuardianData->designation.' '.$stdGuardianData->occupation;
                }
                
                $documents = '';
                if (!empty($student_documents)) {
                    $documents .= '<h5 style="border-bottom: 4px solid #000; font-weight: 500; margin-top: 1rem !important;">Details of Previous Schooling</h5>';
                    $documents .= '<table style="width: 98%; margin: auto; ">';
                    foreach ($student_documents as $key => $val) {
                        $documents .= '<tr>';
                        $documents .= '<td>'.$val->document_type.'</td>';
                        $documents .= '</tr>';
                    }
                    $documents .= '</table>';
                }
                $prevSchool = '';
                if (!empty($student_previous)) {
                    foreach ($student_previous as $key => $sPrev) {
                        if(!empty($sPrev->marks)){
                            $colCount = sizeof($sPrev->marks)*2;
                        }else{
                            $colCount = '';
                        }
                    }

                    $prevSchool .= '<h5 style="border-bottom: 4px solid #000; font-weight: 500; margin-top: 1rem !important;">Details of Previous Schooling</h5>';
                    $prevSchool .= '<table style="width: 98%; margin: auto; ">
                    <tr>
                        <td style="font-size: 11px;">Year</td>
                        <td style="font-size: 11px;">School Name</td>
                        <td style="font-size: 11px;">School Address</td>
                        <td style="font-size: 11px;">Class</td>
                        <td style="font-size: 11px;">Board</td>
                        <td style="font-size: 11px;" colspan="'.$colCount.'" >Grades and marks obtained in Final Exam</td>
                    </tr>';

                    foreach ($student_previous as $key => $value) {
                        $prevSchool .= '<tr>';
                        $prevSchool .= '<td style="font-size: 11px;">'.$value->year.'</td>';
                        $prevSchool .= '<td style="font-size: 11px;">'.$value->school_name.'</td>';
                        $prevSchool .= '<td style="font-size: 11px;">'.$value->school_address.'</td>';
                        $prevSchool .= '<td style="font-size: 11px;">'.$value->class.'</td>';
                        $board = $value->board;
                        if ($value->board == 'Others') {
                           $board = $value->board.''.$value->board_other;
                        }
                        $prevSchool .= '<td style="font-size: 11px;">'.$board.'</td>';

                        if (!empty($value->marks)) {
                            foreach ($value->marks as $key => $val) {
                                $prevSchool.= '<td style="font-size: 11px;">'.$val->sub_name.'</td>';
                                $prevSchool.= '<td style="font-size: 11px;">'.$val->percentage.'% '.' ('.$val->grade.')'.'</td>';
                            }
                        }
                        $prevSchool .= '</tr>';
                    }
                    $prevSchool .= '</table>';
                }

                $issued_data->certificate_data = str_replace('%%school_logo%%', '<span id="school_logo">' . $logo_link . '</span>', $issued_data->certificate_data);
                $issued_data->certificate_data = str_replace('%%school_seal%%', '<span id="school_seal">' . $seal_link . '</span>', $issued_data->certificate_data);
                $issued_data->certificate_data = str_replace('%%signature%%', '<span id="signature">' . $signature_link . '</span>', $issued_data->certificate_data);
                $issued_data->certificate_data = str_replace('%%admission_no%%', '<span id="admission_no">' . $stdData->admission_no . '</span>', $issued_data->certificate_data);
                $issued_data->certificate_data = str_replace('%%nationality%%', '<span id="nationality">' . $stdData->nationality . '</span>', $issued_data->certificate_data);
                $issued_data->certificate_data = str_replace('%%sts_number%%', '<span id="sts_number">' . $stdData->sts_number . '</span>', $issued_data->certificate_data);
                $issued_data->certificate_data = str_replace('%%tc_number%%', '<span id="tc_number">' . $stdData->tc_number . '</span>', $issued_data->certificate_data);
                $issued_data->certificate_data =  str_replace('%%student_name%%', '<span id="student_name">' . $stdData->stdName . '</span>', $issued_data->certificate_data);
                $issued_data->certificate_data = str_replace('%%class%%', '<span id="class">' . $stdData->className . '/' . $stdData->sectionName . '</span>', $issued_data->certificate_data);
                $issued_data->certificate_data = str_replace('%%class_name%%', '<span id="class_name">' . $stdData->className . '</span>', $issued_data->certificate_data);
                $issued_data->certificate_data =  str_replace('%%gender%%', '<span id="gender">' . $g . '</span>', $issued_data->certificate_data);
                $issued_data->certificate_data =  str_replace('%%father_name%%', '<span id="father_name">' . $stdFatherData->pName . '</span>', $issued_data->certificate_data);
                $issued_data->certificate_data = str_replace('%%mother_name%%', '<span id="mother_name">' . $stdMotherData->pName . '</span>', $issued_data->certificate_data);
                $issued_data->certificate_data =  str_replace('%%address%%', '<span id="address">' . $student_address . '</span>', $issued_data->certificate_data);
                $issued_data->certificate_data =  str_replace('%%student_address%%', '<span id="address">' . $stu_address . '</span>', $issued_data->certificate_data);
                $issued_data->certificate_data =  str_replace('%%His/Her%%', '<span id="His/Her">' . $a . '</span>', $issued_data->certificate_data);
                $issued_data->certificate_data =  str_replace('%%Mr/Ms%%', '<span id="Mr/Ms">' . $c . '</span>', $issued_data->certificate_data);
                $issued_data->certificate_data =  str_replace('%%S/D%%', '<span id="S/D">' . $b . '</span>', $issued_data->certificate_data);
                $issued_data->certificate_data = str_replace('%%student_email_id%%', '<span id="student_email_id">' .$stdData->email. '</span>', $issued_data->certificate_data);
                $issued_data->certificate_data = str_replace('%%student_number%%', '<span id="student_number">' .$stdData->preferred_contact_no. '</span>', $issued_data->certificate_data);
                $issued_data->certificate_data = str_replace('%%admission_year%%', '<span id="admission_year">' .$stdData->admission_year. '</span>', $issued_data->certificate_data);
                $issued_data->certificate_data = str_replace('%%stu_address%%', '<span id="stu_address">' .$stdData->birthplace. '</span>', $issued_data->certificate_data);
                $issued_data->certificate_data = str_replace('%%registration_no%%', '<span id="registration_no">' .$stdData->registration_no. '</span>', $issued_data->certificate_data);
                $issued_data->certificate_data = str_replace('%%electives%%', '<span id="electives">' .$stdData->electives. '</span>', $issued_data->certificate_data);
                $issued_data->certificate_data = str_replace('%%terminate_date%%', '<span id="terminate_date">' .$stdData->terminate_date. '</span>', $issued_data->certificate_data);
                $issued_data->certificate_data = str_replace('%%board%%', '<span id="terminate_date">' .$stdData->board. '</span>', $issued_data->certificate_data);
                $issued_data->certificate_data = str_replace('%%first_joined_class%%', '<span id="first_joined_class">' .$first_joined_class. '</span>', $issued_data->certificate_data);

                $issued_data->certificate_data = str_replace('%%dob%%', '<span id="dob">' . date($date_format, strtotime($stdData->dob)) . '</span>', $issued_data->certificate_data);
                $issued_data->certificate_data = str_replace('%%dob_in_date%%', '<span id="dob_date">' . date($date_format, strtotime($stdData->dob)) . '</span>', $issued_data->certificate_data);
                $issued_data->certificate_data = str_replace('%%dob_in_words%%', '<span id="dob_in_words">' . $dob_in_words . '</span>', $issued_data->certificate_data);
                // $issued_data->certificate_data =  str_replace('%%academic_year%%', '<span id="academic_year">academic year</span>', $issued_data->certificate_data);
                $issued_data->certificate_data =  str_replace('%%academic_year%%', '<span id="academic_year">' . $acad_year . '</span>', $issued_data->certificate_data);
                $issued_data->certificate_data =  str_replace('%%fees_amount%%', '<span id="fees_amount">Fees Amount</span>', $issued_data->certificate_data);
                $issued_data->certificate_data =  str_replace('%%fees_concession%%', '<span id="fees_concession">Fees Concession</span>', $issued_data->certificate_data);
                $issued_data->certificate_data = str_replace('%%transportation_location%%','<span id="transportation_location">' . (!empty($std_fees_data->friendly_name) ? $std_fees_data->friendly_name : '') . '</span>',$issued_data->certificate_data);

                $issued_data->certificate_data =  str_replace('%%fees_amount_in_words%%', '<span id="fees_amount_in_words">Fees Amount in words</span>', $issued_data->certificate_data);
                $issued_data->certificate_data =  str_replace('%%promoted_class%%', '<span id="promoted_class">Promoted Class</span>', $issued_data->certificate_data);
                $issued_data->certificate_data =  str_replace('%%date%%', '<span id="date">' . date($date_format) . '</span>', $issued_data->certificate_data);
                $issued_data->certificate_data =  str_replace('%%caste%%', '<span id="caste">' . $stdData->caste . '</span>', $issued_data->certificate_data);
                $issued_data->certificate_data =  str_replace('%%category%%', '<span id="category">' . $stdData->category . '</span>', $issued_data->certificate_data);
                $issued_data->certificate_data =  str_replace('%%prefeered_mobile_no%%', '<span id="prefeered_mobile_no">' . $stdData->contact_no . '</span>', $issued_data->certificate_data);
                $issued_data->certificate_data =  str_replace('%%subjects%%', '<span id="subjects">' .''. '</span>', $issued_data->certificate_data);
                $issued_data->certificate_data =  str_replace('%%bonafide_on%%', '<span id="bonafide_on">' .''. '</span>', $issued_data->certificate_data);
                $issued_data->certificate_data =  str_replace('%%bonafied_issued_for%%', '<span id="bonafied_issued_for">' .''. '</span>', $issued_data->certificate_data);
                $issued_data->certificate_data =  str_replace('%%from_class%%', '<span id="from_class">' .''. '</span>', $issued_data->certificate_data);
                $issued_data->certificate_data =  str_replace('%%to_class%%', '<span id="to_class">' .''. '</span>', $issued_data->certificate_data);
                $issued_data->certificate_data =  str_replace('%%from_academic_year%%', '<span id="from_academic_year">' .$stdData->admission_acad_year.  '</span>', $issued_data->certificate_data);
                $issued_data->certificate_data =  str_replace('%%to_academic_year%%', '<span id="to_academic_year">' .$stdData->current_acad_year. '</span>', $issued_data->certificate_data);
                $issued_data->certificate_data =  str_replace('%%mother_tongue%%', '<span id="mother_tongue">' .$stdData->mother_tongue. '</span>', $issued_data->certificate_data);
                $issued_data->certificate_data =  str_replace('%%combination%%', '<span id="combination">' .$stdData->combination. '</span>', $issued_data->certificate_data);
                $issued_data->certificate_data =  str_replace('%%sc_st%%', '<span id="sc_st">' .''. '</span>', $issued_data->certificate_data);
                $issued_data->certificate_data =  str_replace('%%medium_instruction%%', '<span id="medium_instruction">' .''. '</span>', $issued_data->certificate_data);
                $issued_data->certificate_data = str_replace('%%religion%%', '<span id="religion">' .$stdData->religion. '</span>', $issued_data->certificate_data);
                $issued_data->certificate_data = str_replace('%%promotion_class_name%%', '<span id="promotion_class_name">' .$stdData->promotion_class_name. '</span>', $issued_data->certificate_data);
                $issued_data->certificate_data = str_replace('%%place_of_birth%%', '<span id="place_of_birth">' .$stdData->birthplace. '</span>', $issued_data->certificate_data);
                $issued_data->certificate_data = str_replace('%%blood_group%%', '<span id="blood_group">' .$stdData->blood_group. '</span>', $issued_data->certificate_data);
                $issued_data->certificate_data = str_replace('%%student_aadhar_no%%', '<span id="student_aadhar_no">' .$stdData->aadhar_no. '</span>', $issued_data->certificate_data);
                $issued_data->certificate_data = str_replace('%%father_aadhar_no%%', '<span id="father_aadhar_no">' .$stdFatherData->aadhar_no. '</span>', $issued_data->certificate_data);
                $issued_data->certificate_data = str_replace('%%father_qualification%%', '<span id="father_qualification">' .$stdFatherData->qualification. '</span>', $issued_data->certificate_data);
                $issued_data->certificate_data = str_replace('%%father_profession%%', '<span id="father_profession">' .$stdFatherData->occupation. '</span>', $issued_data->certificate_data);
                $issued_data->certificate_data = str_replace('%%father_designation_office_address%%', '<span id="father_profession">' .$stdFatherData->occupation. '</span>', $issued_data->certificate_data);
                $issued_data->certificate_data = str_replace('%%father_number%%', '<span id="father_number">' .$stdFatherData->mobile_no. '</span>', $issued_data->certificate_data);

                $issued_data->certificate_data = str_replace('%%father_office_number%%', '<span id="father_office_number"></span>', $issued_data->certificate_data);

                $issued_data->certificate_data = str_replace('%%dob_month%%', '<span id="dob">' . date("d F Y", strtotime($stdData->dob)) . '</span>', $issued_data->certificate_data);

                $issued_data->certificate_data = str_replace('%%father_residential_number%%', '<span id="father_residential_number"></span>', $issued_data->certificate_data);
                $issued_data->certificate_data = str_replace('%%father_email_id%%', '<span id="father_email_id">' .$stdFatherData->email. '</span>', $issued_data->certificate_data);
                $issued_data->certificate_data = str_replace('%%father_annual_income%%', '<span id="father_annual_income">' .$stdFatherData->annual_income. '</span>', $issued_data->certificate_data);

                $father_address = ''; // Initialize with a default value
                if (!empty($stdFatherData->addresses) && is_array($stdFatherData->addresses)) {
                    $address = $stdFatherData->addresses[0];
                    $f_address_parts = [];
                    if (!empty($address->Address_line1)) {
                        $f_address_parts[] = $address->Address_line1;
                    }
                    if (!empty($address->Address_line2)) {
                        $f_address_parts[] = $address->Address_line2;
                    }
                    if (!empty($address->area)) {
                        $f_address_parts[] = $address->area;
                    }
                    if (!empty($address->district)) {
                        $f_address_parts[] = $address->district;
                    }
                    if (!empty($address->state)) {
                        $f_address_parts[] = $address->state;
                    }
                    if (!empty($address->country)) {
                        $f_address_parts[] = $address->country;
                    }
                    $pincode = !empty($address->pin_code) ? $address->pin_code : '';
                    $father_address = implode(', ', $f_address_parts);
                    if ($pincode) {
                        $father_address .= ' - ' . $pincode;
                    }
                }
                $issued_data->certificate_data = str_replace('%%father_address%%', '<span id="father_address">' . $father_address . '</span>', $issued_data->certificate_data);

                $mother_address = ''; // Initialize with a default value
                if (!empty($stdMotherData->addresses) && is_array($stdMotherData->addresses)) {
                    $address = $stdMotherData->addresses[0];
                    $m_address_parts = [];
                    if (!empty($address->Address_line1)) {
                        $m_address_parts[] = $address->Address_line1;
                    }
                    if (!empty($address->Address_line2)) {
                        $m_address_parts[] = $address->Address_line2;
                    }
                    if (!empty($address->area)) {
                        $m_address_parts[] = $address->area;
                    }
                    if (!empty($address->district)) {
                        $m_address_parts[] = $address->district;
                    }
                    if (!empty($address->state)) {
                        $m_address_parts[] = $address->state;
                    }
                    if (!empty($address->country)) {
                        $m_address_parts[] = $address->country;
                    }
                    $pincode = !empty($address->pin_code) ? $address->pin_code : '';
                    $mother_address = implode(', ', $m_address_parts);
                    if ($pincode) {
                        $mother_address .= ' - ' . $pincode;
                    }
                }
                $issued_data->certificate_data = str_replace('%%mother_address%%', '<span id="mother_address">' . $mother_address . '</span>', $issued_data->certificate_data);

                $issued_data->certificate_data = str_replace('%%mother_aadhar_no%%', '<span id="mother_aadhar_no">' .$stdMotherData->aadhar_no. '</span>', $issued_data->certificate_data);
                $issued_data->certificate_data = str_replace('%%mother_qualification%%', '<span id="mother_qualification">' .$stdMotherData->qualification. '</span>', $issued_data->certificate_data);
                $issued_data->certificate_data = str_replace('%%mother_profession%%', '<span id="mother_profession">' .$stdMotherData->occupation. '</span>', $issued_data->certificate_data);
                $issued_data->certificate_data = str_replace('%%mother_designation_office_address%%', '<span id="mother_designation_office_address">' .$stdMotherData->occupation. '</span>', $issued_data->certificate_data);
                $issued_data->certificate_data = str_replace('%%mother_number%%', '<span id="mother_number">' .$stdMotherData->mobile_no. '</span>', $issued_data->certificate_data);
                $issued_data->certificate_data = str_replace('%%mother_office_number%%', '<span id="mother_office_number"></span>', $issued_data->certificate_data);
                $issued_data->certificate_data = str_replace('%%mother_residential_number%%', '<span id="mother_residential_number"></span>', $issued_data->certificate_data);
                $issued_data->certificate_data = str_replace('%%mother_email_id%%', '<span id="mother_email_id">' .$stdMotherData->email. '</span>', $issued_data->certificate_data);
                $issued_data->certificate_data = str_replace('%%mother_annual_income%%', '<span id="mother_annual_income">' .$stdMotherData->annual_income. '</span>', $issued_data->certificate_data);

                $issued_data->certificate_data = str_replace('%%guardian_name%%', '<span id="guardian_name">' .$guardianName. '</span>', $issued_data->certificate_data);
                $issued_data->certificate_data = str_replace('%%guardian_email%%', '<span id="guardian_email">' .$guardianEmail. '</span>', $issued_data->certificate_data);
                $issued_data->certificate_data = str_replace('%%guardian_mobile_no%%', '<span id="guardian_mobile_no">' .$guardianMobile. '</span>', $issued_data->certificate_data);
                $issued_data->certificate_data = str_replace('%%guardian_designation_occupation%%', '<span id="guardian_designation_occupation">' .$guardianDesignation. '</span>', $issued_data->certificate_data);


                $issued_data->certificate_data = str_replace('%%electives_studied%%', '<span id="electives_studied">' .''. '</span>', $issued_data->certificate_data);
                $issued_data->certificate_data = str_replace('%%medically_examined%%', '<span id="medically_examined">' .''. '</span>', $issued_data->certificate_data);
                $issued_data->certificate_data =  str_replace('%%doj%%', '<span id="doj">' . date($date_format, strtotime($stdData->date_of_joining)) . '</span>', $issued_data->certificate_data);
                $issued_data->certificate_data =  str_replace('%%doj_in_date%%', '<span id="doj_date">' . date($date_format, strtotime($stdData->date_of_joining)) . '</span>', $issued_data->certificate_data);
                $issued_data->certificate_data =  str_replace('%%sts_number%%', '<span id="sts_number">' . $stdData->sts_number . '</span>', $issued_data->certificate_data);
                $issued_data->certificate_data =  str_replace('%%promoted_to_higher_class%%', '<span id="promoted_to_higher_class">Yes</span>', $issued_data->certificate_data);
                $issued_data->certificate_data =  str_replace('%%is_dues_paid%%', '<span id="is_dues_paid">Yes</span>', $issued_data->certificate_data);

                
                $issued_data->certificate_data =  str_replace('%%joining_year%%', '<span id="joining_year">' . $stdData->joining_year . '</span>', $issued_data->certificate_data);

                $stdImage = '<img width="100" height="100" src="' . site_url() . 'assets/img/icons/profile.png">';
                if ($stdData->picture_url != '')
                    $stdImage = '<img width="100" height="100" src="' . $this->filemanager->getFilePath($stdData->picture_url) . '">';
                $issued_data->certificate_data =  str_replace('%%student_image%%', $stdImage, $issued_data->certificate_data);

                $stdAdmissionImage = '<img width="132px" style="float: right;margin-top: -4rem !important;" height="170px" src="' . site_url() . 'assets/img/icons/profile.png">';
                if ($stdData->picture_url != '')
                    $stdAdmissionImage = '<img width="132px"  style="float: right;margin-top: -8rem !important;"  height="170px" src="' . $this->filemanager->getFilePath($stdData->picture_url) . '">';
                $issued_data->certificate_data =  str_replace('%%student_admission_photo%%', $stdAdmissionImage, $issued_data->certificate_data);
                $issued_data->certificate_data =  str_replace('%%previous_school_details%%', $prevSchool, $issued_data->certificate_data);
                $issued_data->certificate_data =  str_replace('%%application_no%%', $application_no, $issued_data->certificate_data);
                $issued_data->certificate_data =  str_replace('%%document_details%%', $documents, $issued_data->certificate_data);
                $issued_data->certificate_data =  str_replace('%%physical_disability%%', $physical_disability, $issued_data->certificate_data);
                $issued_data->certificate_data =  str_replace('%%sibilingStudying%%', $sibilingStudying, $issued_data->certificate_data);
                $issued_data->certificate_data =  str_replace('%%learning_disability%%', $learning_disability, $issued_data->certificate_data);
                $issued_data->certificate_data =  str_replace('%%sibling_student_name%%', $sibling_student_name, $issued_data->certificate_data);
                $issued_data->certificate_data =  str_replace('%%sibling_school_name%%', $sibling_school_name, $issued_data->certificate_data);
                $issued_data->certificate_data =  str_replace('%%sibling_student_class%%', $sibling_student_class, $issued_data->certificate_data);
                // echo "<pre>"; print_r($issued_data); die();
                // if (!empty($issued_data->template_background)){
                //     $tempalte_Bg_path = $this->filemanager->getFilePath($issued_data->template_background);
                //     $issued_data->certificate_data = str_replace('%%template_background%%', $tempalte_Bg_path, $issued_data->certificate_data);
                //   }else {
                //     $issued_data->certificate_data = str_replace('%%template_background%%','', $issued_data->certificate_data);
                //   }
                if ($revised_certificate) {
                    if (!empty($issued_data->template_background)) {
                        $tempalte_Bg_path = $this->filemanager->getFilePath($issued_data->template_background);
                
                        // Replace src ONLY for img tag with id="background"
                        $issued_data->certificate_data = preg_replace(
                            '/(<img[^>]*id\s*=\s*["\']background["\'][^>]*src\s*=\s*["\'])[^"\']*(["\'])/i',
                            '$1' . $tempalte_Bg_path . '$2',
                            $issued_data->certificate_data
                        );
                    } else {
                        $issued_data->html_content = str_replace('%%template_background%%', '', $issued_data->certificate_data);
                    }
                } else {
                    if (!empty($issued_data->template_background)) {
                        $tempalte_Bg_path = $this->filemanager->getFilePath($issued_data->template_background);
                
                        // Replace empty src in tag with id="background"
                        $issued_data->certificate_data = preg_replace(
                            '/(<img[^>]*id\s*=\s*["\']background["\'][^>]*src\s*=\s*["\'])\s*(["\'])/i',
                            '$1' . $tempalte_Bg_path . '$2',
                            $issued_data->certificate_data
                        );
                    } else {
                        $issued_data->html_content = str_replace('%%template_background%%', '', $issued_data->certificate_data);
                    }
                }
                
                // echo $issued_data->certificate_data;
                $htmlTemplate = $issued_data->certificate_data;
                $escaped_html = $htmlTemplate;
                $escaped_html = str_replace("\n", "", $escaped_html);
                $escaped_html = str_replace("\r", "", $escaped_html);

                preg_match_all('/%%(.*?)%%/', $issued_data->certificate_data, $matches);

                // Ensure placeholders are found
                if (!empty($matches[1])) {
                    // echo "<pre>"; print_r($matches[1]); die();

                    foreach ($matches[1] as $field) {
                        if (in_array($field, $disable_for_edit_fields)) {
                            continue;
                        }
                        $placeholder = isset($stdData->$field) && ! empty($stdData->$field) ? $stdData->$field : "%%$field%%"; // Use $field dynamically
                        $replacement = "<span class='editable' data-field='$field'>$placeholder</span>";
                        // Replace placeholder dynamically
                        if (strpos($issued_data->certificate_data, "%%$field%%") !== false) {
                            $issued_data->certificate_data = str_replace("%%$field%%", $replacement, $issued_data->certificate_data);
                        }
                    }
                }
                echo $issued_data->certificate_data;
                ?>
            </div>
        </div>
        <div class="col-12 text-center" id="pdf-data" style="height: 1000px; display: none;">
        
            

        </div>
    </div>
</div>

<script>
    function printProfile() {
        // var html =  '<?php echo $escaped_html; ?>';
        var html = $("#aadhar").html();
        html = cleanCertificateData(html);
        var stdId = '<?php echo  $stdData->id ?>';
        var template_id = '<?php echo  $issued_data->id ?>';
        $.ajax({
            url: '<?php echo site_url('student/Certificates_controller/save_andprint_certificates') ?>',
            type: 'post',
            data: {
                'html': html,
                'stdId': stdId,
                'template_id': template_id
            },
            success: function(data) {
                console.log(data);
                if (data == '1') {
                    var restorepage = document.body.innerHTML;
                    var printcontent = document.getElementById('aadhar').innerHTML;
                    document.body.innerHTML = printcontent;
                    window.print();
                    document.body.innerHTML = restorepage;
                } else {
                    console.log(data);
                }
            }
        });

    }

    function cleanCertificateData(htmlContent) {
        var cleanedData = htmlContent;

        cleanedData = cleanedData.replace(/<span[^>]*class="[^"]*editable[^"]*"[^>]*>Click to edit reference number<\/span>/g, "%%reference_number%%");
        // Replace placeholders that are not allowed (keep reference_number)
        cleanedData = cleanedData.replace(/%%(?!reference_number)[A-Za-z_]+%%/g, "");

        // Replace editable span specifically for reference_number

        // Remove other editable spans like "Click to edit Name"
        cleanedData = cleanedData.replace(/<span[^>]*class="[^"]*editable[^"]*"[^>]*>Click to edit[^<]*<\/span>/g, "");

        // Remove placeholder spans
        cleanedData = cleanedData.replace(/<span[^>]*class="[^"]*placeholder-text[^"]*"[^>]*>[^<]*<\/span>/g, "");

        // Remove empty editable spans
        cleanedData = cleanedData.replace(/<span[^>]*class="[^"]*editable[^"]*"[^>]*>\s*<\/span>/g, "");

        // Remove unwanted classes
        cleanedData = cleanedData.replace(/\s*class="[^"]*\bplaceholder-text\b[^"]*"/g, "");
        cleanedData = cleanedData.replace(/\s*class="[^"]*\bhas-data\b[^"]*"/g, "");

        return cleanedData;
    }

    function generate_certificate(receipt_number_id) {
        $(".loading-icon").show();
        $("#exporter").hide();
        // var html = $("#aadhar").html();
        var certificate_data = $('#aadhar').html();
        certificate_data = cleanCertificateData(certificate_data);
        var student_id = <?php echo $student_uid; ?>;
        var template_id = <?php echo  $issued_data->id; ?>;
        $.ajax({
            url: '<?php echo site_url('student/Certificates_controller/generate_pdf_certificate'); ?>',
            data: {
                'certificate_data': certificate_data,
                'template_id': template_id,
                'student_id': student_id,
                'manual_feilds_array':manual_feilds_array,
                'receipt_number_id':receipt_number_id
            },
            type: "post",
            success: function(data) {
                var pdf_link = $.parseJSON(data);
                // $(".loading-icon").hide();
                // $("#ten_seconds").hide();
                setTimeout(function(){	
                    // document.getElementById("ten_seconds").style.display="block";
                    window.location.href = "<?php echo site_url('student/Certificates_controller/issue_certificate/'.$student_uid); ?>";
                },4000);
                // $("#pdf-data").show();
                // $("#pdf-data").html('<object data="'+pdf_link+'" type="application/pdf" width="100%" height="100%">');

            },
            error: function(err) {
                console.log(err);
                // location.reload();
            }
        });
    }

    
    var manual_fields_array = [];

    $(document).ready(function () {
        // Process all editable fields on page load
        $(".editable").each(function() {
            var content = $(this).text().trim();
            // Check if content is empty or contains placeholder markers
            if (content === '' || content.includes('%%')) {
                var fieldName = $(this).data("field");
                // Format the field name for better readability
                var formattedFieldName = fieldName.replace(/_/g, ' ').replace(/\b\w/g, function(l) { return l.toUpperCase(); });
                $(this).html("Click to edit " + formattedFieldName);
                $(this).addClass("placeholder-text");
            } else {
                // If data exists, add a subtle edit indicator on hover
                $(this).addClass("has-data");
            }
        });

        // Handle click on editable fields
        $(".editable").click(function () {
            // Save any currently open editing fields
            $(".editing").each(function () {
                saveEditedValue($(this).find("input"));
            });

            if (!$(this).hasClass("editing")) {
                var currentText = $(this).text().trim();
                var fieldName = $(this).data("field");

                // If it's a placeholder, clear it before editing
                if ($(this).hasClass("placeholder-text")) {
                    currentText = "";
                }

                var inputField = $("<input type='text' class='edit-input'>").val(currentText);

                $(this).html(inputField)
                       .addClass("editing")
                       .removeClass("placeholder-text has-data");

                inputField.focus();

                // Select all text in the input field for easy editing
                inputField[0].select();

                // Save on blur
                inputField.blur(function () {
                    saveEditedValue($(this));
                });

                // Save on Enter key
                inputField.keypress(function (e) {
                    if (e.which == 13) { // Enter key
                        saveEditedValue($(this));
                        return false; // Prevent form submission
                    }
                });
            }
        });

        function saveEditedValue(inputElement) {
            var newValue = inputElement.val().trim();
            var parentSpan = inputElement.parent();
            var field = parentSpan.data("field");
            var formattedFieldName = field.replace(/_/g, ' ').replace(/\b\w/g, function(l) { return l.toUpperCase(); });

            // If the value is empty, show the placeholder again
            if (newValue === '') {
                parentSpan.html("Click to edit " + formattedFieldName)
                         .removeClass("editing has-data")
                         .addClass("placeholder-text");
            } else {
                parentSpan.html(newValue)
                         .removeClass("editing placeholder-text")
                         .addClass("has-data");
            }

            // Store updated value in an object
            var obj = {};
            obj[field] = newValue;
            manual_fields_array.push(obj);

            // Apply special logic for specific fields
            if (field === "fees_amount") {
                var amount_in_words = number_in_words(newValue);
                $("#fees_amount_in_words").html(amount_in_words + " Rupees Only")
                                         .removeClass("placeholder-text")
                                         .addClass("has-data");
            }
            else if (field === "dob") {
                var dob = newValue.split("-");
                if (dob.length === 3) {
                    var d = number_in_words(dob[0]);
                    var m = number_in_words(dob[1]);
                    var y = number_in_words(dob[2]);
                    $("#dob_in_words").html(d + ' - ' + m + ' - ' + y)
                                     .removeClass("placeholder-text")
                                     .addClass("has-data");
                }
            }
        }
    });

    var manual_feilds_array=[];

    function addManualValue() {
        var id = $("#manual_fields").val();
        var value = $("#manual_value").val();
        if (id == '')
            return false;
        switch (id) {
            case "fees_amount":
                var amount_in_words = number_in_words(value);
                $("#fees_amount_in_words").html(amount_in_words + " Rupees Only");
                break;
            case "dob":
                var dob = value.split("-");
                var d = number_in_words(dob[0]);
                var m = number_in_words(dob[1]);
                var y = number_in_words(dob[2]);
                $("#dob_in_words").html(d + ' - ' + m + ' - ' + y);
                break;
            default:
        }

        $("#" + id).html(value);

        var obj = {};
        obj[id] = value;
        manual_fields_array.push(obj);
    }

    function number_in_words(amount) {
        var words = new Array();
        words[0] = 'Zero';
        words[1] = 'One';
        words[2] = 'Two';
        words[3] = 'Three';
        words[4] = 'Four';
        words[5] = 'Five';
        words[6] = 'Six';
        words[7] = 'Seven';
        words[8] = 'Eight';
        words[9] = 'Nine';
        words[10] = 'Ten';
        words[11] = 'Eleven';
        words[12] = 'Twelve';
        words[13] = 'Thirteen';
        words[14] = 'Fourteen';
        words[15] = 'Fifteen';
        words[16] = 'Sixteen';
        words[17] = 'Seventeen';
        words[18] = 'Eighteen';
        words[19] = 'Nineteen';
        words[20] = 'Twenty';
        words[30] = 'Thirty';
        words[40] = 'Forty';
        words[50] = 'Fifty';
        words[60] = 'Sixty';
        words[70] = 'Seventy';
        words[80] = 'Eighty';
        words[90] = 'Ninety';
        amount = amount.toString();
        var atemp = amount.split(".");
        var number = atemp[0].split(",").join("");
        var n_length = number.length;
        var words_string = "";
        if (n_length <= 9) {
            var n_array = new Array(0, 0, 0, 0, 0, 0, 0, 0, 0);
            var received_n_array = new Array();
            for (var i = 0; i < n_length; i++) {
                received_n_array[i] = number.substr(i, 1);
            }
            for (var i = 9 - n_length, j = 0; i < 9; i++, j++) {
                n_array[i] = received_n_array[j];
            }
            for (var i = 0, j = 1; i < 9; i++, j++) {
                if (i == 0 || i == 2 || i == 4 || i == 7) {
                    if (n_array[i] == 1) {
                        n_array[j] = 10 + parseInt(n_array[j]);
                        n_array[i] = 0;
                    }
                }
            }
            value = "";
            for (var i = 0; i < 9; i++) {
                if (i == 0 || i == 2 || i == 4 || i == 7) {
                    value = n_array[i] * 10;
                } else {
                    value = n_array[i];
                }
                if (value != 0) {
                    words_string += words[value] + " ";
                }
                if ((i == 1 && value != 0) || (i == 0 && value != 0 && n_array[i + 1] == 0)) {
                    words_string += "Crores ";
                }
                if ((i == 3 && value != 0) || (i == 2 && value != 0 && n_array[i + 1] == 0)) {
                    words_string += "Lakhs ";
                }
                if ((i == 5 && value != 0) || (i == 4 && value != 0 && n_array[i + 1] == 0)) {
                    words_string += "Thousand ";
                }
                if (i == 6 && value != 0 && (n_array[i + 1] != 0 && n_array[i + 2] != 0)) {
                    words_string += "Hundred and ";
                } else if (i == 6 && value != 0) {
                    words_string += "Hundred ";
                }
            }

            words_string = words_string.split(" ").join(" ");
        }
        return words_string;
    }

        $('#manual_fields').on('change', function(){
            var new_val = JSON.parse(<?php echo json_encode($issued_data->edited_data)?>);
            var feilds =$('#manual_fields').val();
            if (new_val[feilds]){
                $('#manual_value').val(new_val[feilds]).$('#addmanual_val').trigger('click');
            }else{
                $('#manual_value').val('');

            }
            
        })
</script>
<style type="text/css">
    @media print {
        h3 {
            font-size: 20px;
        }

        h5 {
            font-size: 20px;
        }

        * {
            font-size: 20px;
        }

        .panel1 {
            padding: 0px;
        }

        .panel-body {
            padding: 0px;
        }

        .heading {
            text-align: center !important;
            font-weight: bold;
            text-decoration: underline;
            font-family: "Times New Roman";
            margin-bottom: 100px;
        }

        .abc {
            color: red !important;
        }
    }

    @page {
        size: A4;
        margin: 5%;
    }

    .ClickWordList {
        list-style-type: none;
    }

    .ClickWordList li {
        padding: 3px;
        /*border-bottom: 1px solid #ccc;*/
        cursor: pointer;
    }

    .ClickWordList li a {
        color: #000;
    }

    /* Editable field styling */
    .editable {
        cursor: pointer;
        padding: 3px 6px;
        border-radius: 4px;
        transition: all 0.3s ease;
        display: inline-block;
        min-width: 60px;
        min-height: 22px;
        border: 1px solid transparent;
        position: relative;
    }

    .editable:hover {
        font-weight: 700;
    }


    /* Placeholder styling */
    .placeholder-text {
        color: #888;
        font-style: italic;
        background-color: #f9f9f9;
        border: 1px dashed #ccc;
        font-size: 0.95em;
    }

    .placeholder-text:hover {
        background-color: #f0f8ff;
        border: 1px dashed #1890ff;
        color: #666;
    }

    /* Active editing state */
    .editing {
        padding: 0;
        border: none;
        background-color: transparent;
    }

    /* Input field styling */
    .edit-input {
        width: 100%;
        padding: 3px 6px;
        border: 2px solid #1890ff;
        border-radius: 4px;
        outline: none;
        box-shadow: 0 0 5px rgba(24, 144, 255, 0.3);
        font-size: 14px;
        color: #333;
        background-color: white;
    }

    .edit-input:focus {
        box-shadow: 0 0 8px rgba(24, 144, 255, 0.5);
    }
</style>

<script type="text/javascript" src="<?php echo base_url();?>assets/js/plugins/jeditor/editable.js"></script>